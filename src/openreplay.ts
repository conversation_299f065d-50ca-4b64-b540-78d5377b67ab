import Tracker from '@openreplay/tracker';
import { dsvApiAxiosInstance } from 'src/dsv-api-axios-instance';
import { AxiosInstance } from 'axios';

export const tracker = new Tracker({
  projectKey: import.meta.env.VITE_OPENREPLAY_PROJECT_KEY!,
  __DISABLE_SECURE_MODE: import.meta.env.NODE_ENV === 'development',
  network: {
    failuresOnly: false,
    sessionTokenHeader: 'x-session-token',
    ignoreHeaders: false,
    capturePayload: true,
    captureInIframes: true,
  },
});

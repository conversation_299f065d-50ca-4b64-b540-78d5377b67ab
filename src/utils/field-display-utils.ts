import { EventRegistrationFormFieldBehavior } from 'src/generated/api/dsv-public/model';

export function shouldDisplayField(
  item: { behavior?: EventRegistrationFormFieldBehavior },
  participantIndex: number
): boolean {
  const behavior = item.behavior || EventRegistrationFormFieldBehavior.ALL;

  switch (behavior) {
    case EventRegistrationFormFieldBehavior.ALL:
      return true;

    case EventRegistrationFormFieldBehavior.ALL_COPIED:
      return true;

    case EventRegistrationFormFieldBehavior.FIRST:
    case EventRegistrationFormFieldBehavior.FIRST_APPLIED_TO_OTHERS:
      return participantIndex === 0;

    case EventRegistrationFormFieldBehavior.SECOND:
      return participantIndex === 1;

    case EventRegistrationFormFieldBehavior.THIRD:
      return participantIndex === 2;

    case EventRegistrationFormFieldBehavior.MORE_THAN_THREE:
      return participantIndex >= 3;

    default:
      return true;
  }
}

export function shouldClearValueAfterParticipantRemoval(
  item: { behavior?: EventRegistrationFormFieldBehavior },
  currentParticipantIndex: number,
  removedParticipantIndex: number
): boolean {
  if (removedParticipantIndex > currentParticipantIndex) {
    return false;
  }

  const willBeVisible = shouldDisplayField(item, currentParticipantIndex);
  return !willBeVisible;
}
